# 默认 Word Counter MCP 服务器

## 概述

为了提升 Ponder 作为写作 IDE 的用户体验，我们在 Cline 中默认集成了 word-counter MCP 服务器。这个服务器专门为中文写作场景设计，提供智能的字数统计功能。

## 功能特性

- ✅ **智能字符识别**：准确识别中文汉字、英文字符、全角和半角字符
- ✅ **多文件支持**：同时统计多个文件的字数
- ✅ **详细统计**：提供每种字符类型的详细统计信息
- ✅ **智能计算**：按照指定规则计算字数并向上取整
- ✅ **自动批准**：`count_words` 工具默认自动批准，无需用户手动确认

## 字数计算规则

| 字符类型 | 计算权重 | 示例 |
|---------|---------|------|
| 中文汉字 | 1.0 | 中、文、汉、字 |
| 英文字符 | 0.5 | a-z, A-Z |
| 全角字符 | 1.0 | ！、（、） |
| 半角字符 | 0.5 | !、(、) |

**注意**：最终字数会向上取整。例如：3.2 → 4，5.7 → 6

## 默认配置

当用户首次使用 Cline 时，系统会自动创建包含以下配置的 MCP 设置文件：

```json
{
  "mcpServers": {
    "word-counter": {
      "command": "npx",
      "args": ["@yoqulin/mcp-word-counter"],
      "disabled": false,
      "autoApprove": ["count_words"],
      "env": {}
    }
  }
}
```

### 配置说明

- **command**: `"npx"` - 使用 npx 运行，无需全局安装
- **args**: `["@yoqulin/mcp-word-counter"]` - 指定要运行的包名
- **disabled**: `false` - 默认启用
- **autoApprove**: `["count_words"]` - 自动批准 count_words 工具
- **env**: `{}` - 无需特殊环境变量

## 使用方法

### 在对话中使用

用户可以直接在与 Cline 的对话中请求字数统计：

```
请统计这个文件的字数：./my-article.md
```

或者统计多个文件：

```
请统计这些文件的字数：./chapter1.md, ./chapter2.md, ./chapter3.md
```

### 工具调用

Cline 会自动调用 `count_words` 工具：

```json
{
  "name": "count_words",
  "arguments": {
    "filePaths": ["./my-article.md"]
  }
}
```

## 输出示例

```
文件: ./my-article.md
总字数: 1,256
详细统计:
  - 汉字: 1,120
  - 英文字符: 145
  - 全角字符: 18
  - 半角字符: 32
  - 其他字符: 3
  - 原始字数: 1,255.50

总计字数: 1,256
```

## 技术实现

### 代码位置

默认 MCP 服务器的配置在 `src/services/mcp/McpHub.ts` 文件的 `getMcpSettingsFilePath` 方法中实现。

### 实现逻辑

1. 检查 MCP 设置文件是否存在
2. 如果不存在，创建包含默认 word-counter 服务器的配置文件
3. 如果已存在，保持现有配置不变

这确保了：
- 新用户自动获得字数统计功能
- 现有用户的配置不会被覆盖
- 用户可以随时禁用或修改配置

## 相关链接

- [word-counter MCP 服务器 GitHub 仓库](https://github.com/yoqu/mcp-word-counter)
- [NPM 包页面](https://www.npmjs.com/package/@yoqulin/mcp-word-counter)
- [MCP 协议文档](https://modelcontextprotocol.io/)

## 故障排除

如果 word-counter 服务器无法正常工作：

1. 检查网络连接，确保可以访问 NPM 仓库
2. 验证 Node.js 版本 >= 18.0.0
3. 检查 MCP 设置文件中的配置是否正确
4. 尝试手动运行 `npx @yoqulin/mcp-word-counter` 测试服务器
5. 查看 Cline 的输出日志获取详细错误信息

如需禁用默认的 word-counter 服务器，可以在 MCP 设置中将 `disabled` 设置为 `true`。
