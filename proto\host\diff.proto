syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "common.proto";

// Provides methods for diff views.
service DiffService {
  // Open the diff view/editor.
  rpc openDiff(OpenDiffRequest) returns (OpenDiffResponse);
  // Get the contents of the diff view.
  rpc getDocumentText(GetDocumentTextRequest) returns (GetDocumentTextResponse);
  // Replace a text selection in the diff.
  rpc replaceText(ReplaceTextRequest) returns (ReplaceTextResponse);
  // Truncate the diff document.
  rpc truncateDocument(TruncateDocumentRequest) returns (TruncateDocumentResponse);
  // Save the diff document.
  rpc saveDocument(SaveDocumentRequest) returns (SaveDocumentResponse);
  // Close the diff editor UI.
  rpc closeDiff(CloseDiffRequest) returns (CloseDiffResponse);
}

message OpenDiffRequest {
  optional cline.Metadata metadata = 1;
  // The absolute path of the document being edited.
  optional string path = 2;
  // The new content for the file.
  optional string content = 3;
}

message OpenDiffResponse {
  // A unique identifier for the diff view that was opened.
  optional string diff_id = 1;
}

message GetDocumentTextRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
}

message GetDocumentTextResponse {
    optional string content = 1;
}

message ReplaceTextRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
  optional string content = 3;
  optional int32 start_line = 4;
  optional int32 end_line = 5;
}

message ReplaceTextResponse {}

message TruncateDocumentRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
  optional int32 end_line = 5;
}

message TruncateDocumentResponse {}

message CloseDiffRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
}

message CloseDiffResponse {}

message SaveDocumentRequest {
  optional cline.Metadata metadata = 1;
  optional string diff_id = 2;
}

message SaveDocumentResponse {}
