---
title: "AWS Bedrock"
description: "Learn how to set up AWS Bedrock with Cline using credentials authentication. This guide covers AWS environment setup, regional access verification, and secure integration with the Cline VS Code extension."
---

### Overview

-   **AWS Bedrock:** A fully managed service that offers access to leading generative AI models (e.g., Anthropic Claude, Amazon Nova) through AWS.\
    [Learn more about AWS Bedrock](https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-bedrock.html).
-   **Cline:** A VS Code extension that acts as a coding assistant by integrating with AI models—empowering developers to generate code, debug, and analyze data.
-   **Enterprise Focus:** This guide is tailored for organizations with established AWS environments (using IAM roles, AWS SSO, AWS Organizations, etc.) to ensure secure and compliant usage.

---

### Step 1: Prepare Your AWS Environment

#### 1.1 Create or Use an IAM Role/User

1. **Sign in to the AWS Management Console:**\
   [AWS Console](https://aws.amazon.com/console/)
2. **Access IAM:**
    - Search for **IAM (Identity and Access Management)** in the AWS Console.
    - Either create a new IAM user or use your enterprise's AWS SSO to assume a dedicated role for Bedrock access.
    - [AWS IAM User Guide](https://docs.aws.amazon.com/IAM/latest/UserGuide/introduction.html)

#### 1.2 Attach the Required Policies

To ensure Cline can interact with AWS Bedrock, your IAM user or role needs specific permissions. While the `AmazonBedrockLimitedAccess` managed policy provides comprehensive access, for a more restricted and secure setup adhering to the principle of least privilege, the following minimal permissions are sufficient for Cline's core model invocation functionality:

-   `bedrock:InvokeModel`
-   `bedrock:InvokeModelWithResponseStream`

You can create a custom IAM policy with these permissions and attach it to your IAM user or role.

**Option 1: Minimal Permissions (Recommended for Production & Least Privilege)**

1.  In the AWS IAM console, create a new policy.
2.  Use the JSON editor to add the following policy document:
    ```json
    {
    	"Version": "2012-10-17",
    	"Statement": [
    		{
    			"Effect": "Allow",
    			"Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"],
    			"Resource": "*" // For enhanced security, scope this to specific model ARNs if possible.
    		}
    	]
    }
    ```
3.  Name the policy (e.g., `ClineBedrockInvokeAccess`) and attach it to your IAM user or role.

**Option 2: Using a Managed Policy (Simpler Initial Setup)**

-   Alternatively, you can attach the AWS managed policy **`AmazonBedrockLimitedAccess`**. This grants broader permissions, including the ability to list models, manage provisioning, and other Bedrock features. This might be simpler for initial setup or if you require these wider capabilities.
    [View AmazonBedrockLimitedAccess Policy Details](https://docs.aws.amazon.com/bedrock/latest/userguide/security-iam.html)

**Important Considerations:**

-   **Model Listing in Cline:** The minimal permissions (`bedrock:InvokeModel`, `bedrock:InvokeModelWithResponseStream`) are sufficient for Cline to _use_ a model if you specify the model ID directly in Cline's settings. If you rely on Cline to dynamically list available Bedrock models, you might need additional permissions like `bedrock:ListFoundationModels`.
-   **AWS Marketplace Subscriptions:** For third-party models (e.g., Anthropic Claude), ensure you have active AWS Marketplace subscriptions. This is typically managed in the AWS Bedrock console under "Model access" and might require `aws-marketplace:Subscribe` permissions if not already handled.
-   _Enterprise Tip:_ Always apply least-privilege practices. Where possible, scope resource ARNs in your IAM policies to specific models or regions. Utilize [Service Control Policies (SCPs)](https://docs.aws.amazon.com/organizations/latest/userguide/orgs_manage_policies_scps.html) for overarching governance in AWS Organizations.

---

### Step 2: Verify Regional and Model Access

#### 2.1 Choose and Confirm a Region

1. **Select a Region:**\
   AWS Bedrock is available in multiple regions (e.g., US East, Europe, Asia Pacific). Choose the region that meets your latency and compliance needs.\
   [AWS Global Infrastructure](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/)
2. **Verify Model Access:**
    - In the AWS Bedrock console, confirm that the models your team requires (e.g., Anthropic Claude, Amazon Nova) are marked as "Access granted."
    - **Note:** Some advanced models might require an [Inference Profile](https://docs.aws.amazon.com/bedrock/latest/userguide/inference-profiles-support.html) if not available on-demand.

#### 2.2 Set Up AWS Marketplace Subscriptions (if needed)

1. **Subscribe to Third-Party Models:**
    - Navigate to the AWS Bedrock console and locate the model subscription section.
    - For models from third-party providers (e.g., Anthropic), accept the terms to subscribe.
    - [AWS Marketplace](https://aws.amazon.com/marketplace/)
2. **Enterprise Tip:**
    - Model subscriptions are often managed centrally. Confirm with your cloud team if a standard subscription process is in place.

---

### Step 3: Configure the Cline VS Code Extension

#### 3.1 Install and Open Cline

1. **Install VS Code:**\
   Download from the [VS Code website](https://code.visualstudio.com/).
2. **Install the Cline Extension:**
    - Open VS Code.
    - Go to the Extensions Marketplace (`Ctrl+Shift+X` or `Cmd+Shift+X`).
    - Search for **Cline** and install it.

#### 3.2 Configure Cline Settings

1. **Open Cline Settings:**
    - Click on the settings ⚙️ to select your API Provider.
2. **Select AWS Bedrock as the API Provider:**
    - From the API Provider dropdown, choose **AWS Bedrock**.
3. **Enter Your AWS Credentials:**
    - Input your **Access Key** and **Secret Key** (or use temporary credentials if using AWS SSO).
    - Specify the correct **AWS Region** (e.g., `us-east-1` or your enterprise-approved region).
4. **Select a Model:**
    - Choose an on-demand model (e.g., **anthropic.claude-3-5-sonnet-20241022-v2:0**).
5. **Save and Test:**
    - Click **Done/Save** to apply your settings.
    - Test the integration by sending a simple prompt (e.g., "Generate a Python function to check if a number is prime.").

---

### Step 4: Security, Monitoring, and Best Practices

1. **Secure Access:**
    - Prefer AWS SSO/federated roles over long-lived IAM credentials.
    - [AWS IAM Best Practices](https://docs.aws.amazon.com/IAM/latest/UserGuide/best-practices.html)
2. **Enhance Network Security:**
    - Consider setting up [AWS PrivateLink](https://docs.aws.amazon.com/vpc/latest/userguide/endpoint-services-overview.html) to securely connect to Bedrock.
3. **Monitor and Log Activity:**
    - Enable AWS CloudTrail to log Bedrock API calls.
    - Use CloudWatch to monitor metrics like invocation count, latency, and token usage.
    - Set up alerts for abnormal activity.
4. **Handle Errors and Manage Costs:**
    - Implement exponential backoff for throttling errors.
    - Use AWS Cost Explorer and set billing alerts to track usage.\
      [AWS Cost Management](https://docs.aws.amazon.com/cost-management/latest/userguide/what-is-aws-cost-management.html)
5. **Regular Audits and Compliance:**
    - Periodically review IAM roles and CloudTrail logs.
    - Follow internal data privacy and governance policies.

---

### Conclusion

By following these steps, your enterprise team can securely integrate AWS Bedrock with the Cline VS Code extension to accelerate development:

1. **Prepare Your AWS Environment:** Create or use a secure IAM role/user, attach the `AmazonBedrockLimitedAccess` policy, and ensure necessary permissions.
2. **Verify Region and Model Access:** Confirm that your selected region supports your required models and subscribe via AWS Marketplace if needed.
3. **Configure Cline in VS Code:** Install and set up Cline with your AWS credentials and choose an appropriate model.
4. **Implement Security and Monitoring:** Use best practices for IAM, network security, monitoring, and cost management.

For further details, consult the [AWS Bedrock Documentation](https://docs.aws.amazon.com/bedrock/latest/userguide/what-is-bedrock.html) and coordinate with your internal cloud team. Happy coding!

---

_This guide will be updated as AWS Bedrock and Cline evolve. Always refer to the latest documentation and internal policies for up-to-date practices._
