import { createGrpcClient } from "@hosts/vscode/hostbridge/client/host-grpc-client-base"
import { HostBridgeClientProvider } from "@/hosts/host-provider-types"
import * as host from "@shared/proto/index.host"

export const vscodeHostBridgeClient: HostBridgeClientProvider = {
	watchServiceClient: createGrpc<PERSON>lient(host.WatchServiceDefinition),
	workspaceClient: createGrpcClient(host.WorkspaceServiceDefinition),
	envClient: createGrpc<PERSON>lient(host.EnvServiceDefinition),
	windowClient: createGrpc<PERSON>lient(host.WindowServiceDefinition),
	diffClient: createGrpc<PERSON>lient(host.DiffServiceDefinition),
}
