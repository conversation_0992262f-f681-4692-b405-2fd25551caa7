syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "common.proto";

// Provides methods for working with IDE windows and editors.
service WindowService {
  // Opens a text document in the editor and returns editor information.
  rpc showTextDocument(ShowTextDocumentRequest) returns (TextEditorInfo);
  rpc showOpenDialogue(ShowOpenDialogueRequest) returns (SelectedResources);
  rpc showMessage(ShowMessageRequest) returns (SelectedResponse);
  rpc showInputBox(ShowInputBoxRequest) returns (ShowInputBoxResponse);
  rpc showSaveDialog(ShowSaveDialogRequest) returns (ShowSaveDialogResponse);
}

message ShowTextDocumentRequest {
  cline.Metadata metadata = 1;
  string path = 2;
  optional ShowTextDocumentOptions options = 3;
}

// See https://code.visualstudio.com/api/references/vscode-api#TextDocumentShowOptions
message ShowTextDocumentOptions {
  optional bool preview = 1;
  optional bool preserve_focus = 2;
  optional int32 view_column = 3;
}

message TextEditorInfo {
  string document_path = 1;
  optional int32 view_column = 2;
  bool is_active = 3;
}

message ShowOpenDialogueRequest {
  cline.Metadata metadata = 1;
  optional bool can_select_many = 2;
  optional string open_label = 3;
  optional ShowOpenDialogueFilterOption filters = 4;
}

message ShowOpenDialogueFilterOption {
  repeated string files = 1;
}

message SelectedResources {
  repeated string paths = 1;
}

enum ShowMessageType {
  ERROR = 0;
  INFORMATION = 1;
  WARNING = 2;
}

message ShowMessageRequest {
  cline.Metadata metadata = 1;
  ShowMessageType type = 2;
  string message = 3;
  optional ShowMessageRequestOptions options = 4;
}

message ShowMessageRequestOptions {
  repeated string items = 1;
  optional bool modal = 2;
  optional string detail = 3;

}

message SelectedResponse {
  optional string selected_option = 1;
}

message ShowSaveDialogRequest {
  cline.Metadata metadata = 1;
  optional ShowSaveDialogOptions options = 2;
}

message ShowSaveDialogOptions {
  optional string default_path = 1;
  map<string, FileExtensionList> filters = 2;
}

message FileExtensionList {
  repeated string extensions = 1;
}

message ShowSaveDialogResponse {
  optional string selected_path = 1;
}

message ShowInputBoxRequest {
  cline.Metadata metadata = 1;
  string title = 2;
  optional string prompt = 3;
  optional string value = 4;
}

message ShowInputBoxResponse {
  optional string response = 1;
}